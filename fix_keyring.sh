#!/bin/bash

# GNOME Keyring 修复脚本
# 解决 "OS keyring 不可用用于加密" 问题

echo "=== GNOME Keyring 修复脚本 ==="
echo "正在检查和修复 GNOME keyring 配置..."

# 1. 检查必要的包是否已安装
echo "1. 检查必要软件包..."
REQUIRED_PACKAGES=("gnome-keyring" "libsecret-tools" "libpam-gnome-keyring")

for package in "${REQUIRED_PACKAGES[@]}"; do
    if dpkg -l | grep -q "^ii.*$package"; then
        echo "✓ $package 已安装"
    else
        echo "✗ $package 未安装，正在安装..."
        sudo apt update && sudo apt install -y "$package"
    fi
done

# 2. 设置环境变量
echo "2. 设置环境变量..."
export GNOME_KEYRING_CONTROL=/run/user/$(id -u)/keyring
export GNOME_KEYRING_PID=$(pgrep gnome-keyring-daemon)

echo "GNOME_KEYRING_CONTROL=$GNOME_KEYRING_CONTROL"
echo "GNOME_KEYRING_PID=$GNOME_KEYRING_PID"

# 3. 检查 keyring 守护进程
echo "3. 检查 keyring 守护进程..."
if pgrep -x "gnome-keyring-daemon" > /dev/null; then
    echo "✓ gnome-keyring-daemon 正在运行"
else
    echo "✗ gnome-keyring-daemon 未运行，正在启动..."
    gnome-keyring-daemon --start --components=pkcs11,secrets,ssh &
    sleep 2
fi

# 4. 检查控制目录
echo "4. 检查控制目录..."
KEYRING_DIR="/run/user/$(id -u)/keyring"
if [ -d "$KEYRING_DIR" ]; then
    echo "✓ Keyring 控制目录存在: $KEYRING_DIR"
    ls -la "$KEYRING_DIR"
else
    echo "✗ Keyring 控制目录不存在，请重新登录或重启系统"
fi

# 5. 创建永久环境变量设置
echo "5. 创建永久环境变量设置..."
BASHRC_FILE="$HOME/.bashrc"
PROFILE_FILE="$HOME/.profile"

# 添加到 .bashrc
if ! grep -q "GNOME_KEYRING_CONTROL" "$BASHRC_FILE" 2>/dev/null; then
    echo "" >> "$BASHRC_FILE"
    echo "# GNOME Keyring 环境变量" >> "$BASHRC_FILE"
    echo "export GNOME_KEYRING_CONTROL=/run/user/\$(id -u)/keyring" >> "$BASHRC_FILE"
    echo "✓ 已添加环境变量到 ~/.bashrc"
fi

# 添加到 .profile
if ! grep -q "GNOME_KEYRING_CONTROL" "$PROFILE_FILE" 2>/dev/null; then
    echo "" >> "$PROFILE_FILE"
    echo "# GNOME Keyring 环境变量" >> "$PROFILE_FILE"
    echo "export GNOME_KEYRING_CONTROL=/run/user/\$(id -u)/keyring" >> "$PROFILE_FILE"
    echo "✓ 已添加环境变量到 ~/.profile"
fi

# 6. 测试 keyring 功能
echo "6. 测试 keyring 功能..."
TEST_LABEL="gnome_keyring_test_$(date +%s)"
TEST_VALUE="test_password_$(date +%s)"

if echo "$TEST_VALUE" | secret-tool store --label="$TEST_LABEL" application gnome_keyring_test 2>/dev/null; then
    echo "✓ Keyring 存储测试成功"
    
    # 尝试读取
    if RETRIEVED=$(secret-tool lookup application gnome_keyring_test 2>/dev/null) && [ "$RETRIEVED" = "$TEST_VALUE" ]; then
        echo "✓ Keyring 读取测试成功"
        # 清理测试数据
        secret-tool clear application gnome_keyring_test 2>/dev/null
        echo "✓ 测试数据已清理"
    else
        echo "✗ Keyring 读取测试失败"
    fi
else
    echo "✗ Keyring 存储测试失败"
fi

echo ""
echo "=== 修复完成 ==="
echo "如果问题仍然存在，请尝试以下操作："
echo "1. 重新启动您的桌面会话"
echo "2. 重新启动计算机"
echo "3. 检查是否正确登录到 GNOME 会话"
echo ""
echo "当前环境变量："
echo "GNOME_KEYRING_CONTROL=$GNOME_KEYRING_CONTROL"
echo "GNOME_KEYRING_PID=$GNOME_KEYRING_PID" 