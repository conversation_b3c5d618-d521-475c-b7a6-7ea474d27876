/**
 * 主应用初始化模块
 * 协调所有模块的加载和初始化，替代原始的main.js
 */

// 确保所有依赖模块已加载
if (!window.TaixiangApp || !window.TaixiangApp.Utils || !window.TaixiangApp.Navigation || !window.TaixiangApp.TableManager) {
    throw new Error('依赖模块未完全加载，请检查模块加载顺序');
}

// 主应用对象
TaixiangApp.App = {
    initialized: false,
    
    // 应用初始化
    init: function() {
        if (this.initialized) {
            console.log('应用已初始化，跳过重复初始化');
            return;
        }
        
        console.log('开始初始化太享查询应用');
        
        // 初始化各个模块
        this.initializeCore();
        this.initializeExportManager();
        this.initializeTabs();
        this.initializeForms();
        this.initializeSidebar();
        this.handleUrlParams();
        this.setupEventListeners();
        this.finalizeInitialization();
        
        this.initialized = true;
        console.log('太享查询应用初始化完成');
    },
    
    // 初始化核心功能
    initializeCore: function() {
        console.log('初始化核心功能');
        
        // 优化移动设备显示
        TaixiangApp.Utils.optimizeForMobile();
        
        // 设置自动消失提示
        TaixiangApp.Utils.setupAutoDismissAlerts();
        
        // 初始化表格增强功能
        if (typeof TaixiangApp.TableManager.enhanceDataTables === 'function') {
            setTimeout(() => {
                TaixiangApp.TableManager.enhanceDataTables();
            }, 300);
        }
    },
    
    // 初始化导出管理器
    initializeExportManager: function() {
        if (typeof ExportManager !== 'undefined' && ExportManager.init) {
            ExportManager.init({
                fileNamePrefix: '太享数据',
                showSuccessMessage: true,
                includeTimestamp: true,
                confirmBeforeExport: true
            });
            console.log('导出管理器初始化完成');
        }
    },
    
    // 初始化数据选项卡（合并版本）
    initializeTabs: function() {
        console.log('初始化标签页系统');
        
        // 检查URL参数
        const urlParams = new URLSearchParams(window.location.search);
        const tabParam = urlParams.get('tab');
        const customerName = urlParams.get('customerName');
        const date = urlParams.get('date');
        
        // 如果URL中指定了标签页，激活对应的标签页
        if (tabParam) {
            console.log(`URL参数指定标签页: ${tabParam}`);
            const tabLink = document.querySelector(`[data-bs-target="#${tabParam}"]`);
            if (tabLink) {
                const tab = new bootstrap.Tab(tabLink);
                tab.show();
                console.log(`已激活标签页: ${tabParam}`);
            }
        }
        
        // 为选项卡切换事件添加处理器
        const tabLinks = document.querySelectorAll('[data-bs-toggle="tab"]');
        tabLinks.forEach(tabLink => {
            tabLink.addEventListener('shown.bs.tab', (event) => {
                const targetId = event.target.getAttribute('data-bs-target').replace('#', '');
                console.log(`切换到标签页: ${targetId}`);
                
                // 重新设置自动消失提示
                TaixiangApp.Utils.setupAutoDismissAlerts();
                
                // 更新URL参数
                const currentUrl = new URL(window.location);
                currentUrl.searchParams.set('tab', targetId);
                window.history.replaceState({}, '', currentUrl);
                
                // 如果标签页没有数据，则加载数据
                const targetTab = document.getElementById(targetId);
                if (targetTab && !targetTab.querySelector('.data-table')) {
                    TaixiangApp.DataLoader.loadTabData(targetId);
                }
                
                // 如果是逾期订单标签页，进行特殊处理
                if (targetId === 'overdue') {
                    this.handleOverdueTabActivation();
                }
            });
        });
        
                 // 处理初始数据加载
         setTimeout(() => {
             if (tabParam) {
                 console.log(`根据URL参数加载${tabParam}标签页数据`);
                 TaixiangApp.DataLoader.loadTabData(tabParam);
             } else if (customerName) {
                 console.log('根据客户姓名参数加载客户数据');
                 const tabLink = document.querySelector('[data-bs-target="#customer"]');
                 if (tabLink) {
                     const tab = new bootstrap.Tab(tabLink);
                     tab.show();
                 }
                 TaixiangApp.DataLoader.loadCustomerData();
             } else if (date) {
                 console.log('根据日期参数加载筛选数据');
                 const tabLink = document.querySelector('[data-bs-target="#filter"]');
                 if (tabLink) {
                     const tab = new bootstrap.Tab(tabLink);
                     tab.show();
                 }
                 TaixiangApp.DataLoader.loadFilterData();
             }
         }, 200);
     },
    
    // 处理逾期订单标签页激活
    handleOverdueTabActivation: function() {
        console.log('逾期订单标签页激活，应用特殊处理');
        setTimeout(() => {
            // 重新应用表格增强
            TaixiangApp.TableManager.enhanceDataTables();
            
            // 调整表格列宽
            const tableElement = document.querySelector('#overdue .data-table');
            if (tableElement && $.fn.DataTable.isDataTable(tableElement)) {
                $(tableElement).DataTable().columns.adjust().responsive.recalc();
            }
        }, 100);
    },
    
    // 初始化表单
    initializeForms: function() {
        console.log('初始化表单处理');
        
        // 表单提交时显示加载指示器
        const forms = document.querySelectorAll('form');
        forms.forEach(form => {
            form.addEventListener('submit', function(e) {
                // 某些表单需要阻止默认提交
                const formId = this.id;
                if (['dateFilterForm', 'customerSearchForm', 'quickSearchForm'].includes(formId)) {
                    e.preventDefault();
                    
                    // 处理快速搜索表单
                    if (formId === 'quickSearchForm') {
                        TaixiangApp.QuickSearch.handleQuickSearch();
                    }
                    return;
                }
                
                TaixiangApp.Utils.showLoading();
                
                // 记录导航状态
                sessionStorage.setItem('navigatedFromSidebar', 'true');
                
                const linkText = this.textContent || '';
                if (linkText.includes('逾期订单查询')) {
                    sessionStorage.setItem('navigatingToOverdue', 'true');
                }
            });
        });
        
        // 绑定AJAX请求事件
        if (typeof $ !== 'undefined') {
            $(document).ajaxStart(function() {
                TaixiangApp.Utils.showLoading();
            }).ajaxStop(function() {
                TaixiangApp.Utils.hideLoading();
            });
        }
    },


    
    // 初始化侧边栏
    initializeSidebar: function() {
        console.log('初始化侧边栏');
        
        // 初始化侧边栏功能管理器
        TaixiangApp.SidebarFunctionManager.init();
        
        // 使用新的侧边栏管理器V2
        if (typeof TaixiangApp.SidebarManagerV2 !== 'undefined') {
            const sidebarManager = new TaixiangApp.SidebarManagerV2();
            sidebarManager.init();
            console.log('使用侧边栏管理器V2');
        } else if (!window.universalSidebarFix || !window.universalSidebarFix.initialized) {
            // 降级到旧版本管理器
            const sidebarManager = new TaixiangApp.SidebarManager();
            sidebarManager.init();
            console.log('降级使用旧版侧边栏管理器');
        } else {
            console.log('检测到通用侧边栏修复器已初始化，跳过主应用中的SidebarManager初始化');
        }
    },
    
    // 处理URL参数
    handleUrlParams: function() {
        console.log('处理URL参数');
        
        const urlParams = TaixiangApp.Navigation.getUrlParams();
        const tabParam = urlParams.get('tab');
        
        if (tabParam) {
            console.log('检测到URL参数tab=' + tabParam);
            
            if (TaixiangApp.Navigation.activateTab(tabParam)) {
                // 处理特定标签页的初始化
                this.handleSpecificTabInitialization(tabParam, urlParams);
            }
        }
    },
    
    // 处理特定标签页的初始化
    handleSpecificTabInitialization: function(tabParam, urlParams) {
        if (tabParam === 'overdue') {
            const pageParam = parseInt(urlParams.get('page')) || 1;
            
            // 重置分页状态
            TaixiangApp.State.resetOverdueState();
            TaixiangApp.State.overdueData.currentPage = pageParam;
            
            setTimeout(() => {
                if (typeof loadOverdueData === 'function') {
                    loadOverdueData(pageParam, false);
                }
            }, 100);
        } else if (tabParam === 'customer') {
            setTimeout(() => {
                const tableElement = document.querySelector('#customer .data-table');
                if (tableElement && $.fn.DataTable.isDataTable(tableElement)) {
                    $(tableElement).DataTable().columns.adjust().responsive.recalc();
                }
            }, 100);
        }
    },
    
    // 设置事件监听器
    setupEventListeners: function() {
        console.log('设置全局事件监听器');
        
        // 窗口大小变化事件
        window.addEventListener('resize', this.handleWindowResize.bind(this));
        
        // 页面完全加载事件
        window.addEventListener('load', this.handlePageLoad.bind(this));
        
        // 为侧边栏链接添加加载指示器
        const sidebarLinks = document.querySelectorAll('.sidebar .nav-link');
        sidebarLinks.forEach(link => {
            link.addEventListener('click', function() {
                TaixiangApp.Utils.showLoading();
                
                sessionStorage.setItem('navigatedFromSidebar', 'true');
                
                const linkText = this.textContent.trim();
                if (linkText.includes('逾期订单查询')) {
                    sessionStorage.setItem('navigatingToOverdue', 'true');
                }
            });
        });
    },
    
    // 处理窗口大小变化
    handleWindowResize: function() {
        clearTimeout(this.resizeTimer);
        this.resizeTimer = setTimeout(() => {
            console.log('处理窗口大小变化');
            
            // 重新优化移动端显示
            TaixiangApp.Utils.optimizeForMobile();
            
            // 调整所有表格
            const tables = document.querySelectorAll('.data-table');
            tables.forEach(table => {
                if ($.fn.DataTable.isDataTable(table)) {
                    $(table).DataTable().columns.adjust().responsive.recalc();
                }
            });
        }, 250);
    },
    
    // 处理页面加载完成
    handlePageLoad: function() {
        console.log('页面完全加载完成');
        
        setTimeout(() => {
            // 重新应用表格增强功能
            if (typeof TaixiangApp.TableManager.enhanceDataTables === 'function') {
                TaixiangApp.TableManager.enhanceDataTables();
            }
            
            // 处理导航状态
            this.handleNavigationState();
            
            // 隐藏加载指示器
            TaixiangApp.Utils.hideLoading();
        }, 200);
    },
    
    // 处理导航状态
    handleNavigationState: function() {
        const navigatedFromSidebar = sessionStorage.getItem('navigatedFromSidebar') === 'true';
        const navigatingToOverdue = sessionStorage.getItem('navigatingToOverdue') === 'true';
        
        if (navigatedFromSidebar && navigatingToOverdue) {
            console.log('检测到从侧边栏导航到逾期订单页面');
            
            setTimeout(() => {
                const overdueTab = document.getElementById('overdue-tab');
                if (overdueTab) {
                    overdueTab.click();
                    
                    const overdueContainer = document.getElementById('overdue');
                    const hasLoadingIndicator = overdueContainer && 
                        overdueContainer.querySelector('.spinner-border');
                    const hasTable = overdueContainer && 
                        overdueContainer.querySelector('table.data-table');
                    
                    if (hasLoadingIndicator && !hasTable) {
                        console.log('检测到逾期订单加载中但未完成，重新加载');
                        if (typeof loadOverdueData === 'function') {
                            loadOverdueData();
                        }
                    }
                    
                    if (typeof TaixiangApp.TableManager.enhanceDataTables === 'function') {
                        TaixiangApp.TableManager.enhanceDataTables();
                    }
                }
                
                // 清除导航标记
                sessionStorage.removeItem('navigatedFromSidebar');
                sessionStorage.removeItem('navigatingToOverdue');
            }, 300);
        }
    },
    
    // 最终化初始化
    finalizeInitialization: function() {
        console.log('完成初始化设置');
        
        // 如果有特定页面的初始化函数，调用它们
        if (typeof calculatePeriodDevicesCount === 'function' && 
            document.querySelector('#customerTabsContent')) {
            setTimeout(calculatePeriodDevicesCount, 1000);
        }
        
        // 页面加载完成后，确保加载指示器已隐藏
        setTimeout(() => {
            TaixiangApp.Utils.hideLoading();
        }, 500);
    }
};

// 向后兼容的全局函数
window.initializeTabs = TaixiangApp.App.initializeTabs.bind(TaixiangApp.App);
window.registerFormSubmitHandlers = TaixiangApp.App.initializeForms.bind(TaixiangApp.App);

// 当DOM加载完成时自动初始化应用
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM加载完成，开始初始化应用');
    TaixiangApp.App.init();
});

console.log('主应用模块已加载');