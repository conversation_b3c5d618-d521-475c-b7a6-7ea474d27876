/**
 * 数据显示模块
 * 负责将加载的数据以统一格式显示在页面上
 */

// 确保依赖模块已加载
if (!window.TaixiangApp || !window.TaixiangApp.Utils || !window.TaixiangApp.TableManager) {
    throw new Error('TaixiangApp Core 和 TableManager 模块未加载');
}

TaixiangApp.DataDisplay = {
    // 通用数据显示函数 - 统一三个标签页的样式
    displayTabData: function(data, tabId, tabType, additionalOptions = {}) {
        console.log(`显示${tabType}标签页数据 - 标签页ID: ${tabId}`);
        const tabElement = document.getElementById(tabId);
        if (!tabElement) {
            console.error(`找不到标签页元素,ID: ${tabId}`);
            return;
        }
        
        // 保存数据到全局变量，以便导出功能使用
        window[`${tabType}Results`] = data;
        
        // 处理错误情况
        if (data.error) {
            console.error(`获取${tabType}数据失败:`, data.error);
            tabElement.innerHTML = `
                <div class="alert alert-danger mt-3">
                    获取数据失败: ${data.error}
                </div>
            `;
            return;
        }
        
        // 处理无数据情况
        if (!data.results || data.results.length === 0) {
            console.log(`${tabType}标签页无数据`);
            let message = "未找到符合条件的记录。";
            if (tabType === 'overdue') {
                message = "未找到逾期订单记录。";
            } else if (tabType === 'customer') {
                message = "未找到该客户的订单记录。";
            }
            
            tabElement.innerHTML = `
                <div class="alert alert-info mt-3">
                    ${message}
                </div>
            `;
            return;
        }
        
        console.log(`构建${tabType}表格HTML, 数据条数: ${data.results.length}`);
        // 构建表格HTML
        const tableHtml = this.buildDataTableHtml(data, tabType);
        
        // 准备标题文本
        let titleText = `共找到 ${data.results.length} 条符合条件的记录`;
        if (tabType === 'overdue') {
            titleText = `共找到 ${data.results.length} 条逾期记录`;
        } else if (tabType === 'customer') {
            const customerName = additionalOptions.customerName || "客户";
            titleText = `共找到 ${data.results.length} 条 ${customerName} 的订单记录`;
        }
        
        // 准备附加按钮(如客户汇总按钮)
        let additionalButtons = '';
        if (tabType === 'customer' && additionalOptions.customerName) {
            additionalButtons = `
                <button class="btn btn-primary ms-3" onclick="viewCustomerSummary('${additionalOptions.customerName}')">
                    <i class="bi bi-pie-chart"></i> 查看${additionalOptions.customerName}汇总数据
                </button>
            `;
        }
        
        // 构建统一的HTML结构
        let contentHtml = '';
        if (additionalButtons) {
            contentHtml = `
                <div class="d-flex justify-content-between align-items-center pt-3 mb-3">
                    <div class="alert alert-success mb-0 flex-grow-1 me-3 auto-dismiss-alert">
                        ${titleText}
                    </div>
                    ${additionalButtons}
                </div>
            `;
        } else {
            contentHtml = `
                <div class="row pt-3">
                    <div class="col-12">
                        <div class="alert alert-success auto-dismiss-alert">
                            ${titleText}
                        </div>
                    </div>
                </div>
            `;
        }
        
        // 添加表格容器
        contentHtml += `
            <div class="table-responsive">
                ${tableHtml}
            </div>
        `;
        
        // 更新DOM
        tabElement.innerHTML = contentHtml;
        
        // 初始化新表格
        console.log(`初始化${tabType}表格的DataTable`);
        const table = tabElement.querySelector('table.data-table');
        if (table) {
            // 确保表格有正确的类名和属性
            table.classList.add('table', 'table-striped', 'table-hover');
            table.setAttribute('style', 'width:100%');
            
            // 初始化DataTable
            const dataTable = TaixiangApp.TableManager.initDataTable(table);
            
            // 强制重新计算响应式布局
            if (dataTable && dataTable.responsive) {
                setTimeout(() => {
                    console.log(`重新计算${tabType}表格响应式布局`);
                    dataTable.responsive.recalc();
                    dataTable.columns.adjust();
                }, 100);
            }
            
            // 应用增强功能
            console.log(`应用表格增强功能到${tabType}表格`);
            TaixiangApp.TableManager.enhanceDataTables();
            
            // 优化移动设备显示
            TaixiangApp.Utils.optimizeForMobile();
        } else {
            console.error(`找不到${tabType}标签页中的表格元素`);
        }
        
        // 设置自动消失提示
        TaixiangApp.Utils.setupAutoDismissAlerts();
        
        // 自动跳转到相应的标签页
        const tabLink = document.querySelector(`[data-bs-target="#${tabId}"]`);
        if (tabLink) {
            const tab = new bootstrap.Tab(tabLink);
            tab.show();
            console.log(`已自动跳转到${tabType}标签页`);
        }
    },

    // 显示筛选数据
    displayFilterData: function(data) {
        this.displayTabData(data, 'filter', 'filter');
    },

    // 显示逾期订单数据
    displayOverdueData: function(data, append = false) {
        if (append) {
            // 追加模式：将新数据添加到现有表格
            console.log('以追加模式显示逾期订单数据');
            this.appendOverdueData(data);
        } else {
            // 普通模式：完全替换
            this.displayTabData(data, 'overdue', 'overdue');
            
            // 为逾期订单添加"加载更多"按钮
            this.addLoadMoreButton();
        }
    },

    // 显示客户数据
    displayCustomerData: function(data) {
        const urlParams = new URLSearchParams(window.location.search);
        const customerName = urlParams.get('customerName');
        
        this.displayTabData(data, 'customer', 'customer', {
            customerName: customerName
        });
    },

    // 追加逾期订单数据
    appendOverdueData: function(data) {
        const overdueTab = document.getElementById('overdue');
        const existingTable = overdueTab.querySelector('table.data-table tbody');
        
        if (!existingTable || !data.results || data.results.length === 0) {
            console.log('无法追加数据：找不到现有表格或无新数据');
            return;
        }
        
        // 构建新行HTML
        let newRowsHtml = '';
        data.results.forEach(item => {
            newRowsHtml += this.buildTableRow(item, 'overdue');
        });
        
        // 追加新行到现有表格
        existingTable.insertAdjacentHTML('beforeend', newRowsHtml);
        
        // 重新应用表格增强功能
        TaixiangApp.TableManager.enhanceDataTables();
        
        console.log(`已追加 ${data.results.length} 条逾期订单记录`);
    },

    // 添加"加载更多"按钮
    addLoadMoreButton: function() {
        const overdueTab = document.getElementById('overdue');
        const tableContainer = overdueTab.querySelector('.table-responsive');
        
        if (!tableContainer || TaixiangApp.DataLoader.overdueDataState.currentPage >= TaixiangApp.DataLoader.overdueDataState.totalPages) {
            return;
        }
        
        // 检查是否已存在加载更多按钮
        const existingButton = overdueTab.querySelector('.load-more-btn');
        if (existingButton) {
            return;
        }
        
        const loadMoreHtml = `
            <div class="text-center py-3">
                <button class="btn btn-outline-primary load-more-btn" onclick="TaixiangApp.DataLoader.loadMoreOverdueData()">
                    <i class="bi bi-arrow-down-circle"></i> 加载更多逾期订单
                    <span class="badge bg-secondary ms-2">${TaixiangApp.DataLoader.overdueDataState.currentPage}/${TaixiangApp.DataLoader.overdueDataState.totalPages}</span>
                </button>
                <div class="small text-muted mt-1">
                    已显示 ${TaixiangApp.DataLoader.overdueDataState.currentPage * 50} / ${TaixiangApp.DataLoader.overdueDataState.totalRecords} 条记录
                </div>
            </div>
        `;
        
        tableContainer.insertAdjacentHTML('afterend', loadMoreHtml);
    },

    // 构建数据表格HTML
    buildDataTableHtml: function(data, tableType) {
        if (!data.results || data.results.length === 0) {
            return '<p class="text-muted">无数据可显示</p>';
        }
        
        const results = data.results;
        const firstItem = results[0];
        
        // 获取所有可能的字段
        const allFields = new Set();
        results.forEach(item => {
            Object.keys(item).forEach(key => allFields.add(key));
        });
        
        // 转换为数组并应用自定义排序
        let headers = this.sortFieldsByPriority(Array.from(allFields), tableType);
        
        // 构建表头
        let tableHtml = `
            <table class="table table-striped table-hover data-table" style="width:100%">
                <thead class="table-dark">
                    <tr>
                        <th class="dtr-control" style="width: 40px;"></th>
        `;
        
        headers.forEach(header => {
            tableHtml += `<th>${header}</th>`;
        });
        
        tableHtml += `
                    </tr>
                </thead>
                <tbody>
        `;
        
        // 构建表格行
        results.forEach(item => {
            tableHtml += this.buildTableRow(item, tableType, headers);
        });
        
        tableHtml += `
                </tbody>
            </table>
        `;
        
        return tableHtml;
    },

    // 构建单个表格行
    buildTableRow: function(item, tableType, headers = null) {
        if (!headers) {
            headers = Object.keys(item);
        }

        // 获取状态信息用于行样式
        let rowStatus = '';
        if (item['备注'] || item['贷后状态'] || item['账单状态']) {
            rowStatus = item['备注'] || item['贷后状态'] || item['账单状态'];
        }

        let rowHtml = `<tr${rowStatus ? ` data-status="${rowStatus}"` : ''}>`;

        // 添加响应式控制列
        rowHtml += '<td class="dtr-control"></td>';

        headers.forEach(header => {
            let cellValue = item[header] || '';
            let cellClass = '';
            let cellStyle = '';

            // 处理特殊字段样式
            if (header === '客户汇总' && cellValue && tableType === 'customer') {
                const customerName = item['客户姓名'] || item['姓名'] || '';
                cellValue = `
                    <button class="btn btn-sm btn-outline-primary" onclick="viewCustomerSummary('${customerName}')">
                        <i class="bi bi-pie-chart"></i> 查看汇总
                    </button>
                `;
            } else {
                // 应用与逾期订单一致的字段样式
                cellValue = this.formatCellValue(cellValue, header, tableType);

                // 设置单元格样式类
                if (['订单编号', '客户手机', '客户姓名'].includes(header)) {
                    cellStyle = 'white-space: nowrap; min-width: 120px;';
                } else if (['当前待收', '总待收', '成本', '待收金额', '金额'].includes(header)) {
                    cellClass = 'amount-field';
                } else if (['业务', '客服', '产品', '产品类型'].includes(header)) {
                    cellClass = 'category-field';
                } else if (['订单日期', '账单日期', '首次逾期日期'].includes(header)) {
                    cellClass = 'date-field';
                } else if (['逾期天数', '逾期期数', '首次逾期期数'].includes(header)) {
                    cellClass = 'status-field';
                } else if (['备注', '贷后状态', '账单状态'].includes(header)) {
                    cellClass = item[header] || '';
                }
            }

            const classAttr = cellClass ? ` class="${cellClass}"` : '';
            const styleAttr = cellStyle ? ` style="${cellStyle}"` : '';

            rowHtml += `<td${classAttr}${styleAttr}>${cellValue}</td>`;
        });

        rowHtml += '</tr>';
        return rowHtml;
    },

    // 格式化单元格值，与逾期订单样式保持一致
    formatCellValue: function(value, header, tableType) {
        if (!value && value !== 0) return '';

        // 金额字段格式化
        if (['当前待收', '总待收', '成本', '待收金额', '金额'].includes(header)) {
            const numValue = parseFloat(value);
            if (!isNaN(numValue) && numValue > 0) {
                return numValue.toFixed(2);
            }
            return value;
        }

        // 逾期天数和期数字段 - 添加徽章
        if (['逾期天数', '逾期期数'].includes(header)) {
            const numValue = parseInt(value);
            if (!isNaN(numValue) && numValue > 0) {
                return `<span class="badge bg-danger">${value}</span>`;
            }
            return value;
        }

        // 业务和客服字段 - 添加徽章
        if (['业务', '客服'].includes(header)) {
            return `<span class="badge bg-primary">${value}</span>`;
        }

        // 产品和产品类型字段 - 添加徽章
        if (['产品', '产品类型'].includes(header)) {
            if (value.includes('电商')) {
                return `<span class="badge bg-info text-dark" data-product-type="电商">${value}</span>`;
            } else if (value.includes('租赁')) {
                return `<span class="badge bg-info text-dark" data-product-type="租赁">${value}</span>`;
            } else {
                return `<span class="badge bg-info text-dark">${value}</span>`;
            }
        }

        // 日期字段 - 添加徽章
        if (['订单日期', '账单日期', '首次逾期日期'].includes(header)) {
            return `<span class="badge bg-secondary">${value}</span>`;
        }

        // 状态字段 - 添加徽章和颜色
        if (['备注', '贷后状态', '账单状态'].includes(header)) {
            return this.formatStatusValue(value);
        }

        // 长文本处理
        if (typeof value === 'string' && value.length > 50) {
            const displayText = value.substring(0, 50) + '...';
            return `
                <span class="expandable-content" title="${this.escapeHtml(value)}">
                    ${displayText}
                </span>
            `;
        }

        return value;
    },

    // 格式化状态值，与逾期订单样式保持一致
    formatStatusValue: function(status) {
        if (!status) return '';

        let badgeClass = 'bg-light text-dark';

        switch(status) {
            case '账单日':
                badgeClass = 'bg-warning text-dark';
                break;
            case '逾期未还':
            case '严重逾期':
                badgeClass = 'bg-danger text-white';
                break;
            case '逾期还款':
            case '轻微逾期':
            case '催收中':
                badgeClass = 'bg-warning text-dark';
                break;
            case '提前还款':
                badgeClass = 'bg-primary text-white';
                break;
            case '按时还款':
            case '已结清':
            case '正常':
                badgeClass = 'bg-success text-white';
                break;
            case '已取消':
                badgeClass = 'bg-secondary text-white';
                break;
            default:
                badgeClass = 'bg-light text-dark';
        }

        return `<span class="badge ${badgeClass}">${status}</span>`;
    },

    // 根据表格类型和逾期订单字段顺序排序字段
    sortFieldsByPriority: function(fields, tableType) {
        // 参考逾期订单的字段优先级顺序
        const overdueFieldOrder = [
            "订单日期", "订单编号", "客户姓名", "贷后状态", "客户手机",
            "客服归属", "业务归属", "产品", "期数", "总待收", "当前待收",
            "首次逾期期数", "账单日期", "逾期天数", "备注信息"
        ];

        // 为不同表格类型定义字段映射和优先级
        const fieldPriorityMaps = {
            'filter': {
                // 日期筛选字段优先级（参考逾期订单）
                priority: [
                    "订单日期", "订单编号", "客户姓名", "账单状态", "客户手机",
                    "客服", "业务", "产品", "产品类型", "期数", "总待收", "当前待收",
                    "待收金额", "金额", "账单日期", "逾期天数", "逾期期数", "备注", "备注信息", "客户信息备注"
                ],
                // 字段名称映射（标准化不同的字段名）
                mapping: {
                    "贷后状态": "账单状态",
                    "客服归属": "客服",
                    "业务归属": "业务",
                    "手机号码": "客户手机",
                    "手机号": "客户手机",
                    "联系电话": "客户手机"
                }
            },
            'customer': {
                // 客户搜索字段优先级（参考逾期订单）
                priority: [
                    "订单编号", "客户姓名", "贷后状态", "账单状态", "客户手机",
                    "客服", "业务", "产品", "产品类型", "期数", "总待收", "当前待收",
                    "订单日期", "账单日期", "逾期天数", "逾期期数", "客户汇总", "备注", "备注信息", "客户信息备注"
                ],
                mapping: {
                    "客服归属": "客服",
                    "业务归属": "业务",
                    "手机号码": "客户手机",
                    "手机号": "客户手机",
                    "联系电话": "客户手机"
                }
            },
            'overdue': {
                // 逾期订单字段优先级（标准参考）
                priority: overdueFieldOrder,
                mapping: {}
            }
        };

        const config = fieldPriorityMaps[tableType] || fieldPriorityMaps['overdue'];
        const priorityOrder = config.priority;
        const fieldMapping = config.mapping;

        // 标准化字段名称
        const normalizedFields = fields.map(field => fieldMapping[field] || field);

        // 检查是否为期数字段
        const isPeriodNumberField = (field) => /^期数\d+$/.test(field);

        // 分离期数字段和普通字段
        const periodFields = normalizedFields.filter(isPeriodNumberField);
        const regularFields = normalizedFields.filter(field => !isPeriodNumberField(field));

        // 对期数字段进行数字排序
        periodFields.sort((a, b) => {
            const numA = parseInt(a.match(/\d+/)[0]);
            const numB = parseInt(b.match(/\d+/)[0]);
            return numA - numB;
        });

        // 对普通字段按优先级排序
        const sortedRegularFields = [];
        const remainingFields = [...regularFields];

        // 首先按优先级顺序添加字段
        priorityOrder.forEach(priorityField => {
            const index = remainingFields.indexOf(priorityField);
            if (index !== -1) {
                sortedRegularFields.push(priorityField);
                remainingFields.splice(index, 1);
            }
        });

        // 添加剩余字段（按字母顺序）
        remainingFields.sort((a, b) => a.localeCompare(b, 'zh-CN'));
        sortedRegularFields.push(...remainingFields);

        // 分离备注字段和其他普通字段
        const remarkFields = ['备注', '备注信息', '客户信息备注'];
        const remarkFieldsInSorted = [];
        const nonRemarkFields = [];
        
        sortedRegularFields.forEach(field => {
            if (remarkFields.includes(field)) {
                remarkFieldsInSorted.push(field);
            } else {
                nonRemarkFields.push(field);
            }
        });
        
        // 合并结果：非备注字段 + 期数字段 + 备注字段
        const finalOrder = [...nonRemarkFields, ...periodFields, ...remarkFieldsInSorted];

        // 将标准化的字段名映射回原始字段名
        return finalOrder.map(normalizedField => {
            // 查找原始字段名
            const originalField = fields.find(field => 
                (fieldMapping[field] || field) === normalizedField
            );
            return originalField || normalizedField;
        }).filter(field => fields.includes(field)); // 确保只返回实际存在的字段
    },

    // HTML转义函数
    escapeHtml: function(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    },

    // 从HTML内容中提取纯文本
    stripHtml: function(html) {
        if (typeof html !== 'string') {
            return html;
        }
        
        const tempElement = document.createElement('div');
        tempElement.innerHTML = html;
        return tempElement.textContent || tempElement.innerText || html;
    }
};

// 向后兼容的全局函数
window.displayTabData = TaixiangApp.DataDisplay.displayTabData.bind(TaixiangApp.DataDisplay);
window.displayFilterData = TaixiangApp.DataDisplay.displayFilterData.bind(TaixiangApp.DataDisplay);
window.displayOverdueData = TaixiangApp.DataDisplay.displayOverdueData.bind(TaixiangApp.DataDisplay);
window.displayCustomerData = TaixiangApp.DataDisplay.displayCustomerData.bind(TaixiangApp.DataDisplay);
window.buildDataTableHtml = TaixiangApp.DataDisplay.buildDataTableHtml.bind(TaixiangApp.DataDisplay);
window.addLoadMoreButton = TaixiangApp.DataDisplay.addLoadMoreButton.bind(TaixiangApp.DataDisplay);
window.stripHtml = TaixiangApp.DataDisplay.stripHtml.bind(TaixiangApp.DataDisplay);

console.log('数据显示模块已加载'); 