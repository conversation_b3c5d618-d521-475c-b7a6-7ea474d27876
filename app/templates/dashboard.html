{% extends "base.html" %}

{% block title %}太享查询_{{ version }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 引入侧边栏模板 -->
    {% include 'sidebar.html' %}

    <!-- 主内容区 -->
    <div class="main-content">
        <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pb-2 mb-3 border-bottom">
            <h1 class="h2">数据查询</h1>
            <div class="btn-toolbar mb-2 mb-md-0">
                <div class="btn-group mr-2">
                    <button type="button" class="btn btn-sm btn-outline-secondary" onclick="exportDataWithSearch('excel')">
                        <i class="bi bi-file-earmark-excel"></i> 导出Excel
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-secondary" onclick="exportDataWithSearch('csv')">
                        <i class="bi bi-file-earmark-text"></i> 导出CSV
                    </button>
                </div>
            </div>
        </div>

        <!-- 选项卡导航 -->
        <ul class="nav nav-tabs" id="dataTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="filter-tab" data-bs-toggle="tab" data-bs-target="#filter" type="button" role="tab">日期筛选结果</button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="customer-tab" data-bs-toggle="tab" data-bs-target="#customer" type="button" role="tab">客户订单</button>
            </li>
        </ul>

        <!-- 选项卡内容 -->
        <div class="tab-content" id="dataTabsContent">
            <!-- 日期筛选结果 -->
            <div class="tab-pane fade show active" id="filter" role="tabpanel">
                {% if filter_results and filter_results.results and filter_results.results|length > 0 %}
                    <div class="pt-3">
                        <div class="alert alert-success auto-dismiss-alert">
                            共找到 {{ filter_results.results|length }} 条符合条件的记录
                        </div>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-striped table-hover data-table" style="width:100%">
                            <thead>
                                <tr>
                                    <th></th> <!-- 用于响应式详情控制 -->
                                    {% for column in filter_results.columns %}
                                    <th>{{ column }}</th>
                                    {% endfor %}
                                </tr>
                            </thead>
                            <tbody>
                                {% for row in filter_results.results %}
                                <tr {% if '备注' in row %}data-status="{{ row['备注'] }}"{% endif %}>
                                    <td class="dtr-control"></td> <!-- 用于响应式详情控制 -->
                                    {% for column in filter_results.columns %}
                                    <td {% if column == '备注' %}class="{{ row.get(column, '') }}"{% endif %}>
                                        {% if column in row %}
                                            {{ row[column] }}
                                        {% else %}
                                            -
                                        {% endif %}
                                    </td>
                                    {% endfor %}
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% elif filter_results and 'error' in filter_results %}
                    <div class="alert alert-danger mt-3">
                        获取数据失败: {{ filter_results.error }}
                    </div>
                {% else %}
                    <div class="alert alert-info mt-3">
                        请选择日期并点击筛选按钮查询数据。
                    </div>
                {% endif %}
            </div>

            <!-- 客户订单 -->
            <div class="tab-pane fade" id="customer" role="tabpanel">
                {% if customer_results and customer_results.results and customer_results.results|length > 0 %}
                    <div class="d-flex justify-content-between align-items-center pt-3 mb-3">
                        <div class="alert alert-success mb-0 flex-grow-1 me-3 auto-dismiss-alert">
                            共找到 {{ customer_results.results|length }} 条 {{ customer_name }} 的订单记录
                        </div>
                        <button class="btn btn-primary ms-3" onclick="viewCustomerSummary('{{ customer_name }}')">
                            <i class="bi bi-pie-chart"></i> 查看{{ customer_name }}汇总数据
                        </button>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-striped table-hover data-table" style="width:100%" data-type="customer">
                            <thead>
                                <tr>
                                    <th></th> <!-- 用于响应式详情控制 -->
                                    {% for column in customer_results.columns %}
                                    <th>{{ column }}</th>
                                    {% endfor %}
                                </tr>
                            </thead>
                            <tbody>
                                {% for row in customer_results.results %}
                                <tr data-customer="{{ row.get('客户姓名', '') }}">
                                    <td class="dtr-control"></td> <!-- 用于响应式详情控制 -->
                                    {% for column in customer_results.columns %}
                                    <td {% if column.startswith('期数') or column.startswith('客户姓名') %}class="{{ row.get(column, '') | status_class if column.startswith('期数') else '' }}"{% endif %}>
                                        {% if column in row %}
                                            {{ row[column] }}
                                        {% else %}
                                            -
                                        {% endif %}
                                    </td>
                                    {% endfor %}
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% elif customer_results and 'error' in customer_results %}
                    <div class="alert alert-danger mt-3">
                        获取客户数据失败: {{ customer_results.error }}
                    </div>
                {% else %}
                    <div class="alert alert-info mt-3">
                        在侧边栏客户姓名搜索框中输入客户姓名并点击搜索按钮查询数据。
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<!-- 将后端数据存储到全局变量中，供前端JavaScript使用 -->
<script>
{% if filter_results %}
    window.filterResults = {{ filter_results|tojson|safe }};
    console.log('接收到筛选数据:', {
        resultCount: window.filterResults.results ? window.filterResults.results.length : 0,
        columns: window.filterResults.columns
    });
{% endif %}

{% if customer_results %}
    window.customerResults = {{ customer_results|tojson|safe }};
    console.log('接收到客户数据:', {
        resultCount: window.customerResults.results ? window.customerResults.results.length : 0,
        columns: window.customerResults.columns
    });
{% endif %}

// 页面加载时处理URL参数，激活对应的选项卡
document.addEventListener('DOMContentLoaded', function() {
    // 获取URL参数
    const urlParams = new URLSearchParams(window.location.search);
    const tabParam = urlParams.get('tab');
    
    // 如果URL中有tab参数，激活对应的选项卡
    if (tabParam) {
        console.log('检测到URL参数tab=' + tabParam);
        const tabElement = document.getElementById(tabParam + '-tab');
        if (tabElement) {
            // 使用Bootstrap的Tab API激活选项卡
            const tab = new bootstrap.Tab(tabElement);
            tab.show();
            console.log('已激活选项卡: ' + tabParam);
            
            // 如果是客户订单选项卡，确保表格正确初始化
            if (tabParam === 'customer') {
                setTimeout(function() {
                    const tableElement = document.querySelector('#customer .data-table');
                    if (tableElement && $.fn.DataTable.isDataTable(tableElement)) {
                        $(tableElement).DataTable().columns.adjust().responsive.recalc();
                    }
                }, 100);
            }
        }
    }
});
</script>

<!-- 引入新的控制器和主应用脚本 -->
<script src="{{ url_for('static', filename='js/controllers/style-controller.js') }}"></script>
<script src="{{ url_for('static', filename='js/controllers/loading-controller.js') }}"></script>
<script src="{{ url_for('static', filename='js/controllers/data-query-controller.js') }}"></script>
<script src="{{ url_for('static', filename='js/controllers/dashboard-adapter.js') }}"></script>
<script src="{{ url_for('static', filename='js/app.js') }}"></script>

<!-- 引入主JavaScript文件 -->
<script src="{{ url_for('static', filename='js/main.js') }}"></script>
{% endblock %}