{% extends "base.html" %}

{% block title %}太享查询_{{ version }} - 数据汇总{% endblock %}

{% block styles %}
<!-- 引入汇总页面核心样式 -->
<link rel="stylesheet" href="{{ url_for('static', filename='css/pages/summary-page.css') }}">
<!-- 引入增强样式 -->
<link rel="stylesheet" href="{{ url_for('static', filename='css/summary-enhanced.css') }}">
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <!-- 引入侧边栏模板 -->
        {% include 'sidebar.html' %}

        <!-- 主内容区 -->
        <div class="col-md-9 col-lg-10 main-content">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pb-2 mb-3 border-bottom">
                <h1 class="h2">数据汇总分析</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group mr-2">
                        <button type="button" class="btn btn-sm btn-outline-secondary" id="exportChartsBtn">
                            <i class="bi bi-download"></i> 导出图表
                        </button>
                    </div>
                </div>
            </div>

            <!-- 日期范围选择表单 -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="GET" action="{{ url_for('main.summary_view') }}" class="row g-3" data-show-global-loading="true">
                        <div class="col-md-4">
                            <label for="start_date" class="form-label">开始日期</label>
                            <input type="date" class="form-control" id="start_date" name="start_date" value="{{ start_date or today }}">
                        </div>
                        <div class="col-md-4">
                            <label for="end_date" class="form-label">结束日期</label>
                            <input type="date" class="form-control" id="end_date" name="end_date" value="{{ end_date or today }}">
                        </div>
                        <div class="col-md-4 d-flex align-items-end">
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-search"></i> 查询
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- 图表区域 -->
            <div class="row">
                <!-- 订单汇总图表 -->
                <div class="col-md-6">
                    <div class="card mb-4">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">订单月度汇总</h5>
                            <div class="chart-controls">
                                <select class="form-select form-select-sm chart-type-selector" data-chart="orderChart">
                                    <option value="bar">柱状图</option>
                                    <option value="line">折线图</option>
                                    <option value="pie">饼图</option>
                                </select>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="orderChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 逾期汇总图表 -->
                <div class="col-md-6">
                    <div class="card mb-4">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">逾期月度汇总</h5>
                            <div class="chart-controls">
                                <select class="form-select form-select-sm chart-type-selector" data-chart="overdueChart">
                                    <option value="bar">柱状图</option>
                                    <option value="line">折线图</option>
                                </select>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="overdueChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 数据表格区域 -->
            <div class="row">
                <!-- 订单汇总表格 -->
                <div class="col-md-6">
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">订单数据明细</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped table-hover data-table" style="width:100%">
                                    <thead>
                                        <tr>
                                            <th class="dtr-control"></th> <!-- 用于响应式详情控制 -->
                                            <th>月份</th>
                                            <th>电商台数/订单数量</th>
                                            <th>租赁台数/订单数量</th>
                                            <th>总台数/订单数量</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% if order_summary and order_summary|length > 0 %}
                                            {% for item in order_summary %}
                                            <tr>
                                                <td class="dtr-control"></td>
                                                <td>{{ item.月份|default('未知') }}</td>
                                                <td>{{ item.电商台数|default(item.电商订单数量|default(0)) }}</td>
                                                <td>{{ item.租赁台数|default(item.租赁订单数量|default(0)) }}</td>
                                                <td>{{ (item.电商台数|default(item.电商订单数量|default(0))|int) + (item.租赁台数|default(item.租赁订单数量|default(0))|int) }}</td>
                                            </tr>
                                            {% endfor %}
                                        {% else %}
                                            <tr>
                                                <td colspan="5" class="text-center auto-dismiss-alert">暂无数据</td>
                                            </tr>
                                        {% endif %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 逾期汇总表格 -->
                <div class="col-md-6">
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">逾期数据明细</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped table-hover data-table" style="width:100%">
                                    <thead>
                                        <tr>
                                            <th class="dtr-control"></th> <!-- 用于响应式详情控制 -->
                                            <th>月份</th>
                                            <th>电商逾期订单数</th>
                                            <th>租赁逾期订单数</th>
                                            <th>总逾期订单数</th>
                                            <th>逾期金额</th>
                                            <th>逾期率</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% if overdue_summary and overdue_summary|length > 0 %}
                                            {% for item in overdue_summary %}
                                            <tr>
                                                <td class="dtr-control"></td>
                                                <td>{{ item.月份|default('未知') }}</td>
                                                <td>{{ item.电商逾期订单数量|default(0) }}</td>
                                                <td>{{ item.租赁逾期订单数量|default(0) }}</td>
                                                <td>{{ (item.电商逾期订单数量|default(0)|int) + (item.租赁逾期订单数量|default(0)|int) }}</td>
                                                <td>{{ item.逾期金额|default(0) }}</td>
                                                <td>{{ item.逾期率|default(0) }}%</td>
                                            </tr>
                                            {% endfor %}
                                        {% else %}
                                            <tr>
                                                <td colspan="7" class="text-center auto-dismiss-alert">暂无数据</td>
                                            </tr>
                                        {% endif %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 原始汇总数据表格 -->
            {% if summary_data_results and summary_data_results|length > 0 %}
            <div class="row">
                <div class="col-12">
                    <div class="card mb-4">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">详细汇总数据</h5>
                            <div class="btn-toolbar">
                                <div class="dropdown me-2">
                                    <button class="btn btn-sm btn-outline-primary dropdown-toggle" type="button" id="dataViewSelector" data-bs-toggle="dropdown">
                                        <i class="bi bi-grid"></i> 视图
                                    </button>
                                    <ul class="dropdown-menu view-selector">
                                        <li><a class="dropdown-item active" href="#" data-view="tabs">店铺选项卡</a></li>
                                        <li><a class="dropdown-item" href="#" data-view="table">完整表格</a></li>
                                        <li><a class="dropdown-item" href="#" data-view="cards">指标卡片</a></li>
                                    </ul>
                                </div>
                                <button class="btn btn-sm btn-outline-secondary" id="exportSummaryData">
                                    <i class="bi bi-download"></i> 导出
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <!-- 视图内容将通过JavaScript动态加载 -->
                            <div id="summaryDataContainer"></div>
                            
                            <!-- 用于存储汇总数据的隐藏元素 -->
                            <script id="summaryData" type="application/json">
                                {
                                    "headers": {{ summary_data_headers|tojson }},
                                    "summary": {{ summary_data_results|tojson }},
                                    "timing_stats": {{ timing_stats|tojson }}
                                }
                            </script>
                        </div>
                    </div>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- 全局加载指示器 -->
<div class="loading-overlay" id="loadingOverlay" style="display: none;">
    <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">加载中...</span>
    </div>
</div>

{% endblock %}

{% block scripts %}
<!-- 企业级核心模块 -->
<script src="{{ url_for('static', filename='js/pages/summary/error-handler.js') }}"></script>
<script src="{{ url_for('static', filename='js/pages/summary/performance-optimizer.js') }}"></script>

<!-- 功能模块 -->
<script src="{{ url_for('static', filename='js/pages/summary/chart-manager.js') }}"></script>
<script src="{{ url_for('static', filename='js/pages/summary/table-manager.js') }}"></script>
<script src="{{ url_for('static', filename='js/pages/summary/api-client.js') }}"></script>
<script src="{{ url_for('static', filename='js/pages/summary/loading-manager.js') }}"></script>

<!-- 增强功能 -->
<script src="{{ url_for('static', filename='js/data-table-enhanced.js') }}"></script>
<script src="{{ url_for('static', filename='js/summary-enhanced.js') }}"></script>

<!-- 基础框架 -->
<script src="{{ url_for('static', filename='js/main.js') }}"></script>

<!-- 页面控制器（最后加载） -->
<script src="{{ url_for('static', filename='js/pages/summary/summary-page-controller.js') }}"></script>

<!-- 传递图表数据给前端 -->
<script>
// 从服务器获取的数据（保持与原始代码一致）
{% if order_chart_data %}
try {
    window.orderChartData = JSON.parse('{{ order_chart_data|tojson }}');
} catch(e) {
    console.error('解析订单图表数据失败:', e);
    window.orderChartData = {};
}
{% else %}
window.orderChartData = {};
{% endif %}

{% if overdue_chart_data %}
try {
    window.overdueChartData = JSON.parse('{{ overdue_chart_data|tojson }}');
} catch(e) {
    console.error('解析逾期图表数据失败:', e);
    window.overdueChartData = {};
}
{% else %}
window.overdueChartData = {};
{% endif %}
</script>
{% endblock %}