{% extends "base.html" %}

{% block title %}太享查询系统 - 首页{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 引入侧边栏模板 -->
    {% include 'sidebar.html' %}

    <!-- 主内容区 -->
    <div class="main-content">
        <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pb-2 mb-3 border-bottom">
            <h1 class="h2">首页</h1>
            <div class="h6 text-muted">
                <span class="badge bg-primary">{{ version }}</span>
            </div>
        </div>

        <!-- 工具卡片区域 -->
        <div class="row g-4 mb-4">
            <!-- 系统状态卡片 -->
            <div class="col-md-6 col-lg-3">
                <div class="card h-100 border-0 shadow-sm">
                    <div class="card-body">
                        <div class="d-flex align-items-center mb-3">
                            <div class="icon-box bg-primary-light me-3">
                                <i class="bi bi-activity"></i>
                            </div>
                            <h5 class="card-title mb-0">系统状态</h5>
                        </div>
                        <h2 class="mb-2">
                            {% if stats.system_status == 'normal' %}
                                <span class="text-success">
                                    <i class="bi bi-check-circle"></i> 正常
                                </span>
                            {% else %}
                                <span class="text-warning">
                                    <i class="bi bi-exclamation-triangle"></i> 未知
                                </span>
                            {% endif %}
                        </h2>
                        <p class="card-text text-muted">
                            API状态: 
                            {% if stats.api_status == 'online' %}
                                <span class="badge bg-success">在线</span>
                            {% else %}
                                <span class="badge bg-danger">离线</span>
                            {% endif %}
                        </p>
                    </div>
                </div>
            </div>
            
            <!-- 待处理订单卡片 -->
            <div class="col-md-6 col-lg-3">
                <div class="card h-100 border-0 shadow-sm">
                    <div class="card-body">
                        <div class="d-flex align-items-center mb-3">
                            <div class="icon-box bg-info-light me-3">
                                <i class="bi bi-calendar2-check"></i>
                            </div>
                            <h5 class="card-title mb-0">今日待处理</h5>
                        </div>
                        <h2 class="mb-2">{{ stats.today_orders }}</h2>
                        <p class="card-text text-muted">
                            <a href="javascript:void(0);" onclick="AppNavigation.filterByDate('{{ today }}')" class="text-decoration-none">
                                点击查看详情 <i class="bi bi-arrow-right"></i>
                            </a>
                        </p>
                    </div>
                </div>
            </div>
            
            <!-- 逾期订单卡片 -->
            <div class="col-md-6 col-lg-3">
                <div class="card h-100 border-0 shadow-sm">
                    <div class="card-body">
                        <div class="d-flex align-items-center mb-3">
                            <div class="icon-box bg-warning-light me-3">
                                <i class="bi bi-exclamation-circle"></i>
                            </div>
                            <h5 class="card-title mb-0">逾期订单</h5>
                        </div>
                        <h2 class="mb-2">{{ stats.overdue_orders }}</h2>
                        <p class="card-text text-muted">
                            <a href="/overdue" class="text-decoration-none">
                                点击查看详情 <i class="bi bi-arrow-right"></i>
                            </a>
                        </p>
                    </div>
                </div>
            </div>
            
            <!-- 快速搜索卡片 -->
            <div class="col-md-6 col-lg-3">
                <div class="card h-100 border-0 shadow-sm">
                    <div class="card-body">
                        <div class="d-flex align-items-center mb-3">
                            <div class="icon-box bg-success-light me-3">
                                <i class="bi bi-search"></i>
                            </div>
                            <h5 class="card-title mb-0">快速搜索</h5>
                        </div>
                        <form id="quickSearchForm" class="quick-search-form">
                            <div class="input-group mb-2">
                                <input type="text" class="form-control" id="quickSearch" 
                                       placeholder="客户姓名/手机号">
                                <button class="btn btn-primary" type="submit">
                                    <i class="bi bi-search"></i>
                                </button>
                            </div>
                        </form>
                        <p class="card-text text-muted small">
                            输入客户姓名或手机号快速查询
                        </p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 第二行工具卡片 -->
        <div class="row g-4 mb-4">
            <!-- 常用工具卡片 -->
            <div class="col-md-6">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-white py-3">
                        <h5 class="card-title mb-0">常用工具</h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-6 col-sm-4">
                                <a href="{{ url_for('main.query.filter_data') }}" class="quick-link">
                                    <div class="quick-link-icon bg-primary-light">
                                        <i class="bi bi-calendar-date"></i>
                                    </div>
                                    <span>日期筛选</span>
                                </a>
                            </div>
                            <div class="col-6 col-sm-4">
                                <a href="{{ url_for('main.query.overdue_orders') }}" class="quick-link">
                                    <div class="quick-link-icon bg-warning-light">
                                        <i class="bi bi-exclamation-diamond"></i>
                                    </div>
                                    <span>逾期订单</span>
                                </a>
                            </div>
                            {% if user.has_permission('full') %}
                            <div class="col-6 col-sm-4">
                                <a href="{{ url_for('main.summary_view') }}" class="quick-link">
                                    <div class="quick-link-icon bg-info-light">
                                        <i class="bi bi-pie-chart"></i>
                                    </div>
                                    <span>数据汇总</span>
                                </a>
                            </div>
                            {% endif %}
                            <!-- 可以根据需要添加更多快捷工具 -->
                            <div class="col-6 col-sm-4">
                                <a href="#" class="quick-link" id="calculatorLink">
                                    <div class="quick-link-icon bg-success-light">
                                        <i class="bi bi-calculator"></i>
                                    </div>
                                    <span>计算器</span>
                                </a>
                            </div>
                            <div class="col-6 col-sm-4">
                                <a href="#" class="quick-link" id="calendarLink">
                                    <div class="quick-link-icon bg-secondary-light">
                                        <i class="bi bi-calendar3"></i>
                                    </div>
                                    <span>日历</span>
                                </a>
                            </div>
                            <div class="col-6 col-sm-4">
                                <a href="#" class="quick-link" id="todoListLink">
                                    <div class="quick-link-icon bg-danger-light">
                                        <i class="bi bi-check2-square"></i>
                                    </div>
                                    <span>待办事项</span>
                                </a>
                            </div>
                            <div class="col-6 col-sm-4">
                                <a href="{{ url_for('main.contract_generator') }}" class="quick-link">
                                    <div class="quick-link-icon bg-primary-light">
                                        <i class="bi bi-file-earmark-text"></i>
                                    </div>
                                    <span>合同生成</span>
                                </a>
                            </div>
                            <div class="col-6 col-sm-4">
                                <a href="{{ url_for('main.receipt_generator') }}" class="quick-link">
                                    <div class="quick-link-icon bg-success-light">
                                        <i class="bi bi-receipt"></i>
                                    </div>
                                    <span>回执单生成</span>
                                </a>
                            </div>
                            <div class="col-6 col-sm-4">
                                <a href="#" class="quick-link" id="qrcodeGeneratorLink">
                                    <div class="quick-link-icon bg-purple-light">
                                        <i class="bi bi-qr-code"></i>
                                    </div>
                                    <span>二维码生成</span>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 通知公告卡片 -->
            <div class="col-md-6">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-white py-3 d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">通知公告</h5>
                        <span class="badge bg-primary">3 条新消息</span>
                    </div>
                    <div class="card-body p-0">
                        <ul class="list-group list-group-flush notice-list">
                            <li class="list-group-item d-flex align-items-center px-3 py-3">
                                <div class="notice-icon bg-primary-light me-3">
                                    <i class="bi bi-bell"></i>
                                </div>
                                <div class="notice-content">
                                    <h6 class="mb-1">系统更新通知</h6>
                                    <p class="mb-1 text-muted small">系统将于今晚22:00-23:00进行例行维护更新</p>
                                    <span class="text-muted smaller">2023-09-10 15:30</span>
                                </div>
                            </li>
                            <li class="list-group-item d-flex align-items-center px-3 py-3">
                                <div class="notice-icon bg-success-light me-3">
                                    <i class="bi bi-check-circle"></i>
                                </div>
                                <div class="notice-content">
                                    <h6 class="mb-1">新功能上线</h6>
                                    <p class="mb-1 text-muted small">数据汇总功能已上线，欢迎使用体验反馈</p>
                                    <span class="text-muted smaller">2023-09-08 09:15</span>
                                </div>
                            </li>
                            <li class="list-group-item d-flex align-items-center px-3 py-3">
                                <div class="notice-icon bg-warning-light me-3">
                                    <i class="bi bi-exclamation-triangle"></i>
                                </div>
                                <div class="notice-content">
                                    <h6 class="mb-1">操作提醒</h6>
                                    <p class="mb-1 text-muted small">请注意及时处理逾期订单，以免影响业务进度</p>
                                    <span class="text-muted smaller">2023-09-05 16:45</span>
                                </div>
                            </li>
                        </ul>
                    </div>
                    <div class="card-footer bg-white py-2 text-center">
                        <a href="#" class="text-decoration-none">查看全部通知</a>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 小组件区域 -->
        <div class="row g-4">
            <!-- 日历小组件 -->
            <div class="col-md-6">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-white py-3">
                        <h5 class="card-title mb-0">日程安排</h5>
                    </div>
                    <div class="card-body">
                        <div id="miniCalendar" class="mini-calendar"></div>
                    </div>
                </div>
            </div>
            
            <!-- 待办事项小组件 -->
            <div class="col-md-6">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-white py-3 d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">待办事项</h5>
                        <button class="btn btn-sm btn-primary" id="addTodoBtn">
                            <i class="bi bi-plus"></i> 添加
                        </button>
                    </div>
                    <div class="card-body p-0">
                        <ul class="list-group list-group-flush todo-list" id="todoList">
                            <li class="list-group-item d-flex align-items-center px-3 py-3">
                                <div class="form-check me-3">
                                    <input class="form-check-input" type="checkbox" value="" id="todo1">
                                </div>
                                <div class="todo-content">
                                    <h6 class="mb-1">跟进张三客户的订单变更</h6>
                                    <span class="text-muted smaller">截止日期: 今天</span>
                                </div>
                                <div class="ms-auto">
                                    <span class="badge bg-danger">紧急</span>
                                </div>
                            </li>
                            <li class="list-group-item d-flex align-items-center px-3 py-3">
                                <div class="form-check me-3">
                                    <input class="form-check-input" type="checkbox" value="" id="todo2">
                                </div>
                                <div class="todo-content">
                                    <h6 class="mb-1">处理待审核订单</h6>
                                    <span class="text-muted smaller">截止日期: 明天</span>
                                </div>
                                <div class="ms-auto">
                                    <span class="badge bg-warning">中等</span>
                                </div>
                            </li>
                            <li class="list-group-item d-flex align-items-center px-3 py-3">
                                <div class="form-check me-3">
                                    <input class="form-check-input" type="checkbox" value="" id="todo3">
                                </div>
                                <div class="todo-content">
                                    <h6 class="mb-1">整理本周工作总结</h6>
                                    <span class="text-muted smaller">截止日期: 周五</span>
                                </div>
                                <div class="ms-auto">
                                    <span class="badge bg-info">一般</span>
                                </div>
                            </li>
                        </ul>
                    </div>
                    <div class="card-footer bg-white py-2 text-center">
                        <a href="#" class="text-decoration-none" id="viewAllTodos">查看全部</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block styles %}
<style>
    /* 小工具卡片样式 */
    .icon-box {
        width: 50px;
        height: 50px;
        border-radius: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
    }
    
    .bg-primary-light {
        background-color: rgba(13, 110, 253, 0.1);
        color: #0d6efd;
    }
    
    .bg-success-light {
        background-color: rgba(25, 135, 84, 0.1);
        color: #198754;
    }
    
    .bg-warning-light {
        background-color: rgba(255, 193, 7, 0.1);
        color: #ffc107;
    }
    
    .bg-info-light {
        background-color: rgba(13, 202, 240, 0.1);
        color: #0dcaf0;
    }
    
    .bg-danger-light {
        background-color: rgba(220, 53, 69, 0.1);
        color: #dc3545;
    }
    
    .bg-secondary-light {
        background-color: rgba(108, 117, 125, 0.1);
        color: #6c757d;
    }
    
    .bg-purple-light {
        background-color: rgba(138, 43, 226, 0.1);
        color: #8a2be2;
    }
    
    /* 快速链接样式 */
    .quick-link {
        display: flex;
        flex-direction: column;
        align-items: center;
        text-decoration: none;
        color: #495057;
        padding: 10px;
        border-radius: 10px;
        transition: all 0.3s;
    }
    
    .quick-link:hover {
        background-color: #f8f9fa;
        transform: translateY(-3px);
    }
    
    .quick-link-icon {
        width: 40px;
        height: 40px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 18px;
        margin-bottom: 8px;
    }
    
    /* 通知列表样式 */
    .notice-icon {
        width: 40px;
        height: 40px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 18px;
    }
    
    .notice-list .list-group-item {
        transition: background-color 0.2s;
    }
    
    .notice-list .list-group-item:hover {
        background-color: #f8f9fa;
    }
    
    .smaller {
        font-size: 0.7rem;
    }
    
    /* 简化版迷你日历样式 */
    .mini-calendar {
        height: 300px;
        background-color: #fff;
        border-radius: 8px;
    }
    
    .simple-calendar {
        padding: 10px;
    }
    
    .calendar-grid {
        font-size: 12px;
    }
    
    .week-header {
        display: grid;
        grid-template-columns: repeat(7, 1fr);
        gap: 2px;
        margin-bottom: 5px;
    }
    
    .day-header {
        text-align: center;
        font-weight: bold;
        padding: 5px;
        color: #6c757d;
        font-size: 11px;
    }
    
    .days-grid {
        display: grid;
        grid-template-columns: repeat(7, 1fr);
        gap: 2px;
    }
    
    .calendar-day {
        position: relative;
        height: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 4px;
        cursor: pointer;
        transition: background-color 0.2s;
    }
    
    .calendar-day:hover:not(.empty) {
        background-color: #f8f9fa;
    }
    
    .calendar-day.today {
        background-color: #0d6efd;
        color: white;
        font-weight: bold;
    }
    
    .calendar-day.empty {
        cursor: default;
    }
    
    .calendar-day.has-event .day-number {
        font-weight: bold;
    }
    
    .event-dot {
        position: absolute;
        bottom: 2px;
        right: 2px;
        width: 4px;
        height: 4px;
        border-radius: 50%;
    }
    
    /* 待办事项样式 */
    .todo-list .list-group-item {
        transition: background-color 0.2s;
    }
    
    .todo-list .list-group-item:hover {
        background-color: #f8f9fa;
    }
    
    .form-check-input:checked + .todo-content h6 {
        text-decoration: line-through;
        color: #6c757d;
    }
    
    /* 计算器样式 */
    .calculator {
        max-width: 300px;
        margin: 0 auto;
    }
    
    #calcDisplay {
        font-size: 1.2rem;
        font-weight: bold;
        height: 60px;
        background-color: #f8f9fa;
        border: 2px solid #dee2e6;
        text-align: right;
        padding: 5px 15px;
        font-family: 'Courier New', monospace;
        line-height: 1.2;
        resize: none;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
    }
    
    .calc-buttons button {
        height: 50px;
        font-size: 1.1rem;
        font-weight: bold;
        border-radius: 8px;
        transition: all 0.2s ease;
        border: 1px solid #dee2e6;
    }
    
    .calc-buttons .btn-light {
        background-color: #f8f9fa;
        color: #333;
    }
    
    .calc-buttons .btn-light:hover {
        background-color: #e9ecef;
        transform: scale(1.05);
    }
    
    .calc-buttons .btn-warning {
        background-color: #ff9500;
        border-color: #ff9500;
        color: white;
    }
    
    .calc-buttons .btn-warning:hover {
        background-color: #e8890b;
        border-color: #e8890b;
        transform: scale(1.05);
    }
    
    .calc-buttons .btn-warning:active {
        background-color: #d1780a;
        border-color: #d1780a;
    }
    
    /* 数字0按钮特殊样式 */
    .calc-buttons .row:last-child .col-6 button {
        border-radius: 25px;
    }
    
    /* 计算器模态框样式优化 */
    #calculatorModal .modal-content {
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.3);
    }
    
    #calculatorModal .modal-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px 15px 0 0;
    }
    
    #calculatorModal .modal-header .btn-close {
        filter: brightness(0) invert(1);
    }
    
    /* 按钮按下效果 */
    .calc-buttons button:active {
        transform: scale(0.95);
        box-shadow: inset 0 2px 4px rgba(0,0,0,0.2);
    }
</style>
{% endblock %}

{% block scripts %}
<!-- 使用本地简化日历组件，避免外部依赖 -->

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 初始化工作台页面

        // 初始化侧边栏功能
        if (typeof SidebarManager !== 'undefined') {
            const sidebarManager = new SidebarManager();
            sidebarManager.init();
        }

        // 初始化API状态检查
        if (typeof AppNavigation !== 'undefined' && AppNavigation.checkApiStatus) {
            AppNavigation.checkApiStatus();
        }
        
        // 注册表单提交处理器
        const dateFilterForm = document.getElementById('dateFilterForm');
        if (dateFilterForm) {
            dateFilterForm.addEventListener('submit', function(e) {
                e.preventDefault();
                if (typeof AppNavigation !== 'undefined' && AppNavigation.filterByDate) {
                    AppNavigation.filterByDate();
                }
            });
        }
        
        const customerSearchForm = document.getElementById('customerSearchForm');
        if (customerSearchForm) {
            customerSearchForm.addEventListener('submit', function(e) {
                e.preventDefault();
                if (typeof AppNavigation !== 'undefined' && AppNavigation.searchCustomer) {
                    AppNavigation.searchCustomer();
                }
            });
        }
        
        // 初始化简化版迷你日历（纯本地实现）
        const calendarEl = document.getElementById('miniCalendar');
        if (calendarEl) {
            initSimpleCalendar(calendarEl);
        }
        
        // 简化版日历实现
        function initSimpleCalendar(container) {
            const now = new Date();
            const currentYear = now.getFullYear();
            const currentMonth = now.getMonth();
            const today = now.getDate();
            
            // 事件数据
            const events = [
                { date: 10, title: '系统维护', type: 'primary' },
                { date: 15, title: '团队会议', type: 'info' },
                { date: 30, title: '月度总结', type: 'success' }
            ];
            
            const monthNames = [
                '一月', '二月', '三月', '四月', '五月', '六月',
                '七月', '八月', '九月', '十月', '十一月', '十二月'
            ];
            
            const dayNames = ['日', '一', '二', '三', '四', '五', '六'];
            
            // 创建日历HTML
            let html = `
                <div class="simple-calendar">
                    <div class="calendar-header d-flex justify-content-between align-items-center mb-3">
                        <button class="btn btn-sm btn-outline-primary" onclick="changeMonth(-1)">&larr;</button>
                        <h6 class="mb-0">${currentYear}年${monthNames[currentMonth]}</h6>
                        <button class="btn btn-sm btn-outline-primary" onclick="changeMonth(1)">&rarr;</button>
                    </div>
                    <div class="calendar-grid">
                        <div class="week-header">
                            ${dayNames.map(day => `<div class="day-header">${day}</div>`).join('')}
                        </div>
                        <div class="days-grid" id="daysGrid">
                            ${generateCalendarDays(currentYear, currentMonth, today, events)}
                        </div>
                    </div>
                </div>
            `;
            
            container.innerHTML = html;
        }
        
        function generateCalendarDays(year, month, today, events) {
            const firstDay = new Date(year, month, 1).getDay();
            const daysInMonth = new Date(year, month + 1, 0).getDate();
            
            let days = '';
            
            // 空白日期
            for (let i = 0; i < firstDay; i++) {
                days += '<div class="calendar-day empty"></div>';
            }
            
            // 实际日期
            for (let day = 1; day <= daysInMonth; day++) {
                const event = events.find(e => e.date === day);
                const isToday = day === today;
                const classes = ['calendar-day'];
                
                if (isToday) classes.push('today');
                if (event) classes.push('has-event');
                
                days += `
                    <div class="${classes.join(' ')}" title="${event ? event.title : ''}">
                        <span class="day-number">${day}</span>
                        ${event ? `<div class="event-dot bg-${event.type}"></div>` : ''}
                    </div>
                `;
            }
            
            return days;
        }
        
        // 全局函数用于月份切换（简化版本，暂时不实现）
        window.changeMonth = function(direction) {
            console.log('切换月份:', direction);
        }
        
        // 快速搜索表单提交
        const quickSearchForm = document.getElementById('quickSearchForm');
        if (quickSearchForm) {
            quickSearchForm.addEventListener('submit', function(e) {
                e.preventDefault();
                const searchValue = document.getElementById('quickSearch').value.trim();
                if (searchValue) {
                    // 跳转到客户搜索页面
                    window.location.href = `/?customerName=${encodeURIComponent(searchValue)}&tab=customer`;
                }
            });
        }
        
        // 计算器小工具
        const calculatorLink = document.getElementById('calculatorLink');
        if (calculatorLink) {
            calculatorLink.addEventListener('click', function(e) {
                e.preventDefault();
                // 直接显示计算器模态框
                const calculatorModal = new bootstrap.Modal(document.getElementById('calculatorModal'));
                calculatorModal.show();
                // 初始化计算器功能
                initCalculator();
            });
        }
        
        // 计算器功能实现
        function initCalculator() {
            const display = document.getElementById('calcDisplay');
            const buttons = document.querySelectorAll('.calc-buttons button');
            
            let currentInput = '0';
            let previousInput = null;
            let operator = null;
            let waitingForNext = false;
            let expression = ''; // 新增：记录完整表达式
            
            // 更新显示
            function updateDisplay() {
                let displayText = '';
                
                // 如果有运算符在等待，显示完整表达式
                if (operator && !waitingForNext) {
                    displayText = `${previousInput} ${operator} ${currentInput}`;
                } else if (operator && waitingForNext) {
                    displayText = `${previousInput} ${operator}`;
                } else {
                    displayText = currentInput;
                }
                
                // 限制显示长度，优先显示右侧内容
                if (displayText.length > 20) {
                    displayText = '...' + displayText.slice(-17);
                }
                
                display.value = displayText;
            }
            
            // 清除所有
            function clear() {
                currentInput = '0';
                previousInput = null;
                operator = null;
                waitingForNext = false;
                expression = '';
                updateDisplay();
            }
            
            // 输入数字
            function inputNumber(num) {
                if (waitingForNext) {
                    currentInput = num;
                    waitingForNext = false;
                } else {
                    currentInput = currentInput === '0' ? num : currentInput + num;
                }
                updateDisplay();
            }
            
            // 输入小数点
            function inputDecimal() {
                if (waitingForNext) {
                    currentInput = '0.';
                    waitingForNext = false;
                } else if (currentInput.indexOf('.') === -1) {
                    currentInput += '.';
                }
                updateDisplay();
            }
            
            // 输入操作符
            function inputOperator(nextOperator) {
                const inputValue = parseFloat(currentInput);
                
                if (previousInput === null) {
                    previousInput = inputValue;
                } else if (operator) {
                    const currentValue = previousInput || 0;
                    const newValue = calculate(currentValue, inputValue, operator);
                    
                    currentInput = String(newValue);
                    previousInput = newValue;
                } else {
                    previousInput = inputValue;
                }
                
                waitingForNext = true;
                operator = nextOperator;
                updateDisplay();
            }
            
            // 执行计算
            function calculate(firstValue, secondValue, operator) {
                switch (operator) {
                    case '+':
                        return firstValue + secondValue;
                    case '-':
                        return firstValue - secondValue;
                    case '×':
                        return firstValue * secondValue;
                    case '÷':
                        return secondValue !== 0 ? firstValue / secondValue : 0;
                    case '%':
                        return firstValue % secondValue;
                    default:
                        return secondValue;
                }
            }
            
            // 等于运算
            function performCalculation() {
                const inputValue = parseFloat(currentInput);
                
                if (previousInput !== null && operator) {
                    // 显示完整的计算过程
                    const fullExpression = `${previousInput} ${operator} ${inputValue}`;
                    const newValue = calculate(previousInput, inputValue, operator);
                    
                    // 先显示完整表达式和结果
                    display.value = `${fullExpression} = ${newValue}`;
                    
                    // 延迟一秒后显示最终结果
                    setTimeout(() => {
                        currentInput = String(newValue);
                        previousInput = null;
                        operator = null;
                        waitingForNext = true;
                        expression = '';
                        updateDisplay();
                    }, 1000);
                }
            }
            
            // 正负号切换
            function toggleSign() {
                if (currentInput !== '0') {
                    const oldValue = currentInput;
                    currentInput = currentInput.charAt(0) === '-' 
                        ? currentInput.slice(1) 
                        : '-' + currentInput;
                    
                    // 显示操作过程
                    display.value = `±(${oldValue}) = ${currentInput}`;
                    setTimeout(() => {
                        updateDisplay();
                    }, 800);
                }
            }
            
            // 百分号运算
            function percentage() {
                const value = parseFloat(currentInput);
                const oldValue = currentInput;
                currentInput = String(value / 100);
                
                // 显示百分号运算过程
                display.value = `${oldValue}% = ${currentInput}`;
                setTimeout(() => {
                    updateDisplay();
                }, 800);
            }
            
            // 为所有按钮添加事件监听器
            buttons.forEach(button => {
                button.addEventListener('click', function() {
                    const value = this.textContent;
                    
                    // 添加视觉反馈
                    this.style.transform = 'scale(0.95)';
                    this.style.transition = 'transform 0.1s';
                    setTimeout(() => {
                        this.style.transform = '';
                    }, 100);
                    
                    // 为运算符按钮添加特殊反馈
                    if (value === '+' || value === '-' || value === '×' || value === '÷') {
                        this.style.backgroundColor = '#d1780a';
                        setTimeout(() => {
                            this.style.backgroundColor = '';
                        }, 200);
                    }
                    
                    if (value >= '0' && value <= '9') {
                        inputNumber(value);
                    } else if (value === '.') {
                        inputDecimal();
                    } else if (value === '+' || value === '-' || value === '×' || value === '÷') {
                        inputOperator(value);
                    } else if (value === '=') {
                        performCalculation();
                    } else if (value === 'C') {
                        clear();
                    } else if (value === '±') {
                        toggleSign();
                    } else if (value === '%') {
                        percentage();
                    }
                });
            });
            
            // 键盘支持
            document.addEventListener('keydown', function(event) {
                // 只有在计算器模态框打开时才响应键盘事件
                const calculatorModal = document.getElementById('calculatorModal');
                if (!calculatorModal.classList.contains('show')) return;
                
                const key = event.key;
                
                if (key >= '0' && key <= '9') {
                    inputNumber(key);
                } else if (key === '.') {
                    inputDecimal();
                } else if (key === '+') {
                    inputOperator('+');
                } else if (key === '-') {
                    inputOperator('-');
                } else if (key === '*') {
                    inputOperator('×');
                } else if (key === '/') {
                    event.preventDefault(); // 阻止浏览器搜索
                    inputOperator('÷');
                } else if (key === 'Enter' || key === '=') {
                    performCalculation();
                } else if (key === 'Escape' || key === 'c' || key === 'C') {
                    clear();
                } else if (key === 'Backspace') {
                    if (currentInput.length > 1) {
                        currentInput = currentInput.slice(0, -1);
                    } else {
                        currentInput = '0';
                    }
                    updateDisplay();
                }
            });
            
            // 初始化显示
            clear();
        }
        
        // 待办事项复选框处理
        document.querySelectorAll('.todo-list .form-check-input').forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                const todoItem = this.closest('.list-group-item');
                if (this.checked) {
                    todoItem.style.opacity = '0.6';
                } else {
                    todoItem.style.opacity = '1';
                }
            });
        });
        
        // 添加待办事项按钮
        const addTodoBtn = document.getElementById('addTodoBtn');
        if (addTodoBtn) {
            addTodoBtn.addEventListener('click', function() {
                // 显示添加待办事项模态框
                const todoModal = new bootstrap.Modal(document.getElementById('todoModal'));
                todoModal.show();
            });
        }
        
        // 二维码生成器按钮
        const qrcodeGeneratorLink = document.getElementById('qrcodeGeneratorLink');
        if (qrcodeGeneratorLink) {
            qrcodeGeneratorLink.addEventListener('click', function(e) {
                e.preventDefault();
                // 显示二维码生成器模态框
                const qrcodeModal = new bootstrap.Modal(document.getElementById('qrcodeModal'));
                qrcodeModal.show();
            });
        }
    });
</script>

<!-- 计算器模态框 -->
<div class="modal fade" id="calculatorModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-sm">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">计算器</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="calculator">
                    <div class="form-group mb-3">
                        <input type="text" class="form-control text-end" id="calcDisplay" readonly>
                    </div>
                    <div class="calc-buttons">
                        <div class="row g-1 mb-1">
                            <div class="col-3"><button class="btn btn-light w-100">C</button></div>
                            <div class="col-3"><button class="btn btn-light w-100">±</button></div>
                            <div class="col-3"><button class="btn btn-light w-100">%</button></div>
                            <div class="col-3"><button class="btn btn-warning w-100">÷</button></div>
                        </div>
                        <div class="row g-1 mb-1">
                            <div class="col-3"><button class="btn btn-light w-100">7</button></div>
                            <div class="col-3"><button class="btn btn-light w-100">8</button></div>
                            <div class="col-3"><button class="btn btn-light w-100">9</button></div>
                            <div class="col-3"><button class="btn btn-warning w-100">×</button></div>
                        </div>
                        <div class="row g-1 mb-1">
                            <div class="col-3"><button class="btn btn-light w-100">4</button></div>
                            <div class="col-3"><button class="btn btn-light w-100">5</button></div>
                            <div class="col-3"><button class="btn btn-light w-100">6</button></div>
                            <div class="col-3"><button class="btn btn-warning w-100">-</button></div>
                        </div>
                        <div class="row g-1 mb-1">
                            <div class="col-3"><button class="btn btn-light w-100">1</button></div>
                            <div class="col-3"><button class="btn btn-light w-100">2</button></div>
                            <div class="col-3"><button class="btn btn-light w-100">3</button></div>
                            <div class="col-3"><button class="btn btn-warning w-100">+</button></div>
                        </div>
                        <div class="row g-1">
                            <div class="col-6"><button class="btn btn-light w-100">0</button></div>
                            <div class="col-3"><button class="btn btn-light w-100">.</button></div>
                            <div class="col-3"><button class="btn btn-warning w-100">=</button></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 添加待办事项模态框 -->
<div class="modal fade" id="todoModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">添加待办事项</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="todoForm">
                    <div class="mb-3">
                        <label for="todoTitle" class="form-label">标题</label>
                        <input type="text" class="form-control" id="todoTitle" required>
                    </div>
                    <div class="mb-3">
                        <label for="todoDueDate" class="form-label">截止日期</label>
                        <input type="date" class="form-control" id="todoDueDate">
                    </div>
                    <div class="mb-3">
                        <label for="todoPriority" class="form-label">优先级</label>
                        <select class="form-select" id="todoPriority">
                            <option value="low">低</option>
                            <option value="medium">中</option>
                            <option value="high">高</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="todoDesc" class="form-label">备注</label>
                        <textarea class="form-control" id="todoDesc" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="saveTodoBtn">保存</button>
            </div>
        </div>
    </div>
</div>

<!-- 二维码生成器模态框 -->
<div class="modal fade" id="qrcodeModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-qr-code me-2"></i>二维码生成器
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="qrcodeForm">
                    <div class="mb-3">
                        <label for="qrcodeContent" class="form-label">内容</label>
                        <textarea class="form-control" id="qrcodeContent" rows="4" 
                                placeholder="请输入要生成二维码的内容，如网址、文本、联系方式等..."></textarea>
                        <div class="form-text">支持网址、文本、联系方式等各种内容</div>
                    </div>
                    <div class="mb-3">
                        <label for="qrcodeSize" class="form-label">尺寸</label>
                        <select class="form-select" id="qrcodeSize">
                            <option value="128">128x128</option>
                            <option value="256" selected>256x256</option>
                            <option value="512">512x512</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <button type="button" class="btn btn-primary" id="generateQrcodeBtn">
                            <i class="bi bi-gear-fill me-1"></i>生成二维码
                        </button>
                    </div>
                    <div id="qrcodeResult" class="text-center" style="display: none;">
                        <div class="mb-3">
                            <img id="qrcodeImage" src="" alt="生成的二维码" class="border rounded">
                        </div>
                        <div class="d-flex gap-2 justify-content-center">
                            <button type="button" class="btn btn-success btn-sm" id="downloadQrcodeBtn">
                                <i class="bi bi-download me-1"></i>下载PNG
                            </button>
                            <button type="button" class="btn btn-info btn-sm" id="copyQrcodeBtn">
                                <i class="bi bi-clipboard me-1"></i>复制到剪贴板
                            </button>
                        </div>
                    </div>
                    <div id="qrcodeError" class="alert alert-danger" style="display: none;"></div>
                    <div id="qrcodeLoading" class="text-center" style="display: none;">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">生成中...</span>
                        </div>
                        <div class="mt-2">生成中，请稍候...</div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>

<script>
// 二维码生成器功能
document.addEventListener('DOMContentLoaded', function() {
    const generateBtn = document.getElementById('generateQrcodeBtn');
    const contentInput = document.getElementById('qrcodeContent');
    const sizeSelect = document.getElementById('qrcodeSize');
    const resultDiv = document.getElementById('qrcodeResult');
    const errorDiv = document.getElementById('qrcodeError');
    const loadingDiv = document.getElementById('qrcodeLoading');
    const qrcodeImage = document.getElementById('qrcodeImage');
    const downloadBtn = document.getElementById('downloadQrcodeBtn');
    const copyBtn = document.getElementById('copyQrcodeBtn');
    
    if (generateBtn) {
        generateBtn.addEventListener('click', async function() {
            const content = contentInput.value.trim();
            const size = parseInt(sizeSelect.value);
            
            if (!content) {
                showError('请输入要生成二维码的内容');
                return;
            }
            
            // 显示加载状态
            showLoading();
            
            try {
                const response = await fetch('/generate_qrcode', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        content: content,
                        size: size
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    // 显示生成的二维码
                    qrcodeImage.src = result.image;
                    showResult();
                    
                    // 设置下载功能
                    downloadBtn.onclick = function() {
                        downloadImage(result.image, `qrcode_${Date.now()}.png`);
                    };
                    
                    // 设置复制功能
                    copyBtn.onclick = function() {
                        copyImageToClipboard(result.image);
                    };
                } else {
                    showError(result.error || '生成二维码失败');
                }
            } catch (error) {
                showError('网络错误，请重试');
                console.error('生成二维码错误:', error);
            }
        });
    }
    
    // 支持回车键生成
    if (contentInput) {
        contentInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                generateBtn.click();
            }
        });
    }
    
    function showLoading() {
        resultDiv.style.display = 'none';
        errorDiv.style.display = 'none';
        loadingDiv.style.display = 'block';
    }
    
    function showResult() {
        loadingDiv.style.display = 'none';
        errorDiv.style.display = 'none';
        resultDiv.style.display = 'block';
    }
    
    function showError(message) {
        loadingDiv.style.display = 'none';
        resultDiv.style.display = 'none';
        errorDiv.textContent = message;
        errorDiv.style.display = 'block';
    }
    
    function downloadImage(dataUrl, filename) {
        const link = document.createElement('a');
        link.href = dataUrl;
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        // 显示成功提示
        showToast('二维码已下载', 'success');
    }
    
    async function copyImageToClipboard(dataUrl) {
        try {
            // 将base64转换为blob
            const response = await fetch(dataUrl);
            const blob = await response.blob();
            
            // 复制到剪贴板
            await navigator.clipboard.write([
                new ClipboardItem({ [blob.type]: blob })
            ]);
            
            showToast('二维码已复制到剪贴板', 'success');
        } catch (error) {
            console.error('复制失败:', error);
            showToast('复制失败，请尝试手动保存', 'error');
        }
    }
    
    function showToast(message, type = 'info') {
        // 简单的提示功能
        const toast = document.createElement('div');
        toast.className = `alert alert-${type === 'success' ? 'success' : 'danger'} position-fixed`;
        toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        toast.textContent = message;
        
        document.body.appendChild(toast);
        
        setTimeout(() => {
            toast.remove();
        }, 3000);
    }
});
</script>

<!-- 侧边栏功能已统一由SidebarManagerV2管理，无需额外脚本 -->

{% endblock %} 